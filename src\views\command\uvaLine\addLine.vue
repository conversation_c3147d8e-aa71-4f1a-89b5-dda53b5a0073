<template>
  <div class="app-container" v-loading="isLoading">
    <div class="w-map" id="uvaline-map"></div>

    <div class="w-right">
        <div class="w-line-name">
            <span>{{fromData.routesInfo.routesName}}</span>
            <i @click="editLineName" class="el-icon-edit" v-if="fromData.isImport !== '1'"></i>
        </div>

        <div class="w-info">
            <div>
                <p>{{this.fromData.routesPointList.length}}</p>
                <p>航点数</p>
            </div>
            <div>
                <p>{{routesLength}}Km</p>
                <p>预计飞行里程</p>
            </div>
            <div>
                <p>{{(routesLength*1000 / fromData.routesConfig.autoFlightSpeed / 60).toFixed(1) }}min</p>
                <p>预计飞行时间</p>
            </div>
        </div>

        <div class="w-switch">
            <div :class="{'w-active':isDropLine}" @click="isDropLine = true">航线设置</div>
            <div :class="{'w-active':!isDropLine}" @click="switchDropLine">航点设置</div>
        </div>

        <div class="w-line-info" v-if="isDropLine">
            <div class="w-cell">
                <div class="w-title">
                    <span>飞行速度</span>
                    <span>{{fromData.routesConfig.autoFlightSpeed}}m/s</span>
                </div>
                <div class="w-content">
                    <div class="w-content-img">
                        <img @click="fromData.isImport !== '1' && fromData.routesConfig.autoFlightSpeed--" src="@/assets/images/jian.png" alt="" :class="{'disabled': fromData.isImport === '1'}">
                    </div>
                    <div class="w-content-slider">
                        <el-slider :min="1" :max="15" v-model="fromData.routesConfig.autoFlightSpeed" :disabled="fromData.isImport === '1'"></el-slider>
                    </div>
                    <div class="w-content-img">
                        <img @click="fromData.isImport !== '1' && fromData.routesConfig.autoFlightSpeed++" src="@/assets/images/jia.png" alt="" :class="{'disabled': fromData.isImport === '1'}">
                    </div>
                </div>
            </div>

            <div class="w-cell">
                <div class="w-title">
                    <span>全局飞行高度（相对地面高度）</span>
                    <span>{{fromData.routesConfig.globalHeight}}m</span>
                </div>
                <div class="w-content">
                    <div class="w-content-img">
                        <img @click="fromData.isImport !== '1' && editGlobalHeight('-')" src="@/assets/images/jian.png" alt="" :class="{'disabled': fromData.isImport === '1'}">
                    </div>
                    <div class="w-content-slider">
                        <el-slider @change="globalHeightChange"  :min="50" :max="500" v-model="fromData.routesConfig.globalHeight" :disabled="fromData.isImport === '1'"></el-slider>
                    </div>
                    <div class="w-content-img">
                        <img @click="fromData.isImport !== '1' && editGlobalHeight('+')" src="@/assets/images/jia.png" alt="" :class="{'disabled': fromData.isImport === '1'}">
                    </div>
                </div>
            </div>

            <div class="w-cell">
                <div class="w-title">
                    <span>无人机偏航角</span>
                    <span></span>
                </div>
                <div class="w-content">
                    <w-select :value.sync="fromData.routesConfig.waypointHeadingMode" :option="waypointHeadingModeOption" :disabled="fromData.isImport === '1'" />
                </div>
            </div>

            <div class="w-cell">
                <div class="w-title">
                    <span>完成动作</span>
                    <span></span>
                </div>
                <div class="w-content">
                    <w-select :value.sync="fromData.routesConfig.finishAction" :option="finishActionOption" :disabled="fromData.isImport === '1'" />
                </div>
            </div>

            <div class="w-cell">
                <div class="w-title">
                    <span>机型</span>
                    <span></span>
                </div>
                <div class="w-content">
                    <w-select :value.sync="fromData.routesConfig.droneSubEnumValue" :option="droneSubEnumValueOption" :disabled="fromData.isImport === '1'" />
                </div>
            </div>

        </div>

        <div class="w-line-info" v-else>
            <div class="w-carousel">
                <i class="el-icon-caret-left" @click="fromData.isImport !== '1' && switchPoint('-')" :class="{'disabled': fromData.isImport === '1'}"></i>
                <span>航点{{routesPointObj.index + 1}}</span>
                <i class="el-icon-caret-right" @click="fromData.isImport !== '1' && switchPoint('+')" :class="{'disabled': fromData.isImport === '1'}"></i>
            </div>
            <div class="w-line-info-box">

                <div class="w-cell">
                    <div class="w-title">
                        <span>飞行高度</span>
                        <span>{{routesPointObj.height}}m</span>
                    </div>
                    <div class="w-content">
                        <div class="w-content-img">
                            <img @click="fromData.isImport !== '1' && editPointHeight('-')" src="@/assets/images/jian.png" alt="" :class="{'disabled': fromData.isImport === '1'}">
                        </div>
                        <div class="w-content-slider">
                            <el-slider @change="pointHeightChange" :min="50" :max="500" v-model="routesPointObj.height" :disabled="fromData.isImport === '1'"></el-slider>
                        </div>
                        <div class="w-content-img">
                            <img @click="fromData.isImport !== '1' && editPointHeight('+')" src="@/assets/images/jia.png" alt="" :class="{'disabled': fromData.isImport === '1'}">
                        </div>
                    </div>
                </div>

                <div class="w-cell">
                    <div class="w-title">
                        <span>云台俯仰角</span>
                        <span>{{routesPointObj.gimbalPitchAngle}}°</span>
                    </div>
                    <div class="w-content">
                        <div class="w-content-img">
                            <img @click="fromData.isImport !== '1' && setPointGimbalPitchAngle1('-')" src="@/assets/images/jian.png" alt="" :class="{'disabled': fromData.isImport === '1'}">
                        </div>
                        <div class="w-content-slider">
                            <el-slider @change="setPointGimbalPitchAngle" :min="-90" :max="30" v-model="routesPointObj.gimbalPitchAngle" :disabled="fromData.isImport === '1'"></el-slider>
                        </div>
                        <div class="w-content-img">
                            <img @click="fromData.isImport !== '1' && setPointGimbalPitchAngle1('+')" src="@/assets/images/jia.png" alt="" :class="{'disabled': fromData.isImport === '1'}">
                        </div>
                    </div>
                </div>

                <div class="w-cell">
                    <div class="w-title">
                        <span>无人机偏航角</span>
                        <span></span>
                    </div>
                    <div class="w-content">
                        <w-select :value.sync="routesPointObj.waypointHeadingMode" :option="waypointHeadingModeOption" :disabled="fromData.isImport === '1'" />
                    </div>
                </div>

                <div class="w-cell">
                    <div class="w-title">
                        <span>添加动作</span>
                        <span></span>
                    </div>
                    <div class="w-content">
                        <w-select :isselect="true" @change="selectAction" title="添加动作" :option="routesPointActionListOption" :disabled="fromData.isImport === '1'" />
                    </div>
                </div>

                <div class="w-action">
                    <draggable v-model="routesPointObj.routesPointActionList" :options="dragOptions"  @update="actionDraggable" handle=".el-icon-rank" :disabled="fromData.isImport === '1'">
                        <transition-group tag="div" id="doing" class="item-d">
                            <div class="w-cell" v-for="item in routesPointObj.routesPointActionList" :key="item.index">
                                <div class="w-title">
                                    <div class="w-title-left">
                                        <i class="el-icon-rank" :class="{'disabled': fromData.isImport === '1'}"></i>
                                        <span>{{getActionLabel(item.actionActuatorFunc)}}</span>
                                    </div>
                                    <div class="w-title-right">
                                        <span v-if="item.actionActuatorFunc == 'zoom'">{{item.value}}倍</span>
                                        <span v-if="item.actionActuatorFunc == 'rotateYaw'">{{item.value}}°</span>
                                        <span v-if="item.actionActuatorFunc == 'hover'">{{item.value}}s</span>
                                        <span v-if="item.actionActuatorFunc == 'multipleTiming'">{{item.value}}s</span>
                                        <span v-if="item.actionActuatorFunc == 'multipleDistance'">{{item.value}}m</span>
                                        <i class="el-icon-delete" @click="fromData.isImport !== '1' && deleteAction(item)" :class="{'disabled': fromData.isImport === '1'}" v-if="fromData.isImport !== '1'"></i>
                                    </div>
                                </div>
                                <div class="w-content" v-if="needSlider(item.actionActuatorFunc)">
                                    <div class="w-content-img">
                                        <img @click="fromData.isImport !== '1' && editActionActuatorFunc1(item, '-')" src="@/assets/images/jian.png" alt="" :class="{'disabled': fromData.isImport === '1'}">
                                    </div>
                                    <div class="w-content-slider">
                                        <el-slider
                                        :min="item.actionActuatorFunc == 'rotateYaw'? -180 : 1"
                                        :max="actionMax(item.actionActuatorFunc)"
                                        @change="editActionActuatorFunc(item)"
                                        v-model="item.value"
                                        :disabled="fromData.isImport === '1'"></el-slider>
                                    </div>
                                    <div class="w-content-img">
                                        <img @click="fromData.isImport !== '1' && editActionActuatorFunc1(item, '+')" src="@/assets/images/jia.png" alt="" :class="{'disabled': fromData.isImport === '1'}">
                                    </div>
                                </div>
                            </div>
                        </transition-group>
                    </draggable>
                </div>


            </div>

        </div>

        <div class="w-btn">
            <el-button type="primary" @click="addRouteLine" :disabled="fromData.isImport === '1'">保存设置</el-button>
        </div>
    </div>

    <div class="w-right-tool">
      <el-tooltip class="item" effect="dark" content="位置搜索" placement="top">
        <img
          @click="ShowAddressSearch"
          src="@/assets/images/search.png"
          alt=""
        />
      </el-tooltip>
    </div>
    <div class="w-right-search" v-if="isShowAddressSearch">
      <el-select
        style="width: 100%; height: 40px"
        v-model="addressSearch"
        filterable
        remote
        reserve-keyword
        placeholder="请输入关键词"
        :remote-method="remoteMethod"
        @change="addressSelect"
        :loading="addressSearchLoading"
      >
        <el-option
          v-for="item in addressOptions"
          :key="item.id"
          :label="item.name"
          :value="item.id"
        >
        </el-option>
      </el-select>
    </div>

    <div class="w-delete" v-if="showDeleteBtn" :style="{'top':deleteBtnTop + 'px', 'left': deleteBtnLeft + 'px'}" @click="deletePoint">删 除</div>


    <div class="w-x-y-z" v-if="false">
        <div class="w-cell">
            <div class="w-title">
                <span>Heading</span>
                <span>{{Heading}}度</span>
            </div>
            <div class="w-content">
                <div class="w-content-img">
                    <img @click="editHeading('-',1)" src="@/assets/images/jian.png" alt="">
                </div>
                <div class="w-content-slider">
                    <el-slider @change="HeadingPitchRollChange" :min="0" :max="360" v-model="Heading"></el-slider>
                </div>
                <div class="w-content-img">
                    <img @click="editHeading('+',1)" src="@/assets/images/jia.png" alt="">
                </div>
            </div>
        </div>
        <div class="w-cell">
            <div class="w-title">
                <span>Pitch</span>
                <span>{{Pitch}}度</span>
            </div>
            <div class="w-content">
                <div class="w-content-img">
                    <img @click="editHeading('-',2)" src="@/assets/images/jian.png" alt="">
                </div>
                <div class="w-content-slider">
                    <el-slider @change="HeadingPitchRollChange" :min="0" :max="360"  v-model="Pitch"></el-slider>
                </div>
                <div class="w-content-img">
                    <img @click="editHeading('+',2)" src="@/assets/images/jia.png" alt="">
                </div>
            </div>
        </div>
        <div class="w-cell">
            <div class="w-title">
                <span>Roll</span>
                <span>{{Roll}}度</span>
            </div>
            <div class="w-content">
                <div class="w-content-img">
                    <img @click="editHeading('-',3)" src="@/assets/images/jian.png" alt="">
                </div>
                <div class="w-content-slider">
                    <el-slider @change="HeadingPitchRollChange" :min="0" :max="360"  v-model="Roll"></el-slider>
                </div>
                <div class="w-content-img">
                    <img @click="editHeading('+',3)" src="@/assets/images/jia.png" alt="">
                </div>
            </div>
        </div>
    </div>

  </div>
</template>
  
  <script>
import { addRoutes, routesDet, putRoutes, addNotAirportRoutes, putNotAirportRoutes } from "@/api/cruise/airline";
import { uploadPlanImg } from "@/api/command/plan";
import { listAirport } from "@/api/cruise/airdrome";
import html2canvas from "html2canvas";
import { createUniqueString } from "@/utils/index";
import wSelect from "@/views/components/lineselect"
import draggable from 'vuedraggable'
import { tiandiVec_w, tiandiVav_w, tiandiImg_w, tiandiCia_w} from '@/utils/cesium/tiandiLayer.js';
import { cesiumToken } from "@/utils/use-g-map"
import { getAssistantInputtips, getCurrentLocation, getWgs84togcj02, getRegeo } from "@/api/g-map-api"
import { wgs84togcj02, gcj02towgs84} from "@/utils/cesium/coordtransform";
import frustum from "./mixins/frustum"

import AMapLoader from "@amap/amap-jsapi-loader";
import { amapKey, amapSecretkey } from "@/utils/use-g-map"

export default {
    name: "Dict",
    mixins: [frustum],
    components: { 
        wSelect,
        draggable 
    },
    data() {
        return {
            viewer: null, // 地图实例
            helper: null, // 地图事件监听器
            zoom: 10,
            // geocoder: null, // 经纬度转地址插件对象
            // placeSearch: null,  // 存放地图搜索对象
            isShowAddressSearch: false, //是否显示搜索
            addressSearch: "", // 地址选择
            addressOptions: [], // 地址搜索列表
            addressSearchLoading: false, // 地址搜索加载
            addressMarker: null, // 搜索到地址的点位对象
            addressSearchId: null, // 搜索到地址标注对象id
            isaddLayer: false,
            satellite: null, //卫星图层
            // 机场相关
            airportList: [], // 机场列表
            airportMarkers: [], // 机场标记列表
            sector: { // 机场安全区域信息
                areaNum: 8, // 区域数量
                tilt: 10, // 倾斜角
                height: 150, // 默认高度
                areaList: [
                    {areaName: "A区", radius: 3000},
                    {areaName: "B区", radius: 4000},
                    {areaName: "C区", radius: 5000},
                    {areaName: "D区", radius: 2000},
                    {areaName: "E区", radius: 1000},
                    {areaName: "F区", radius: 3500},
                    {areaName: "G区", radius: 3000},
                    {areaName: "H区", radius: 4000},
                ],
                constructAreaList: [], // 超高建筑点位
            },
            fromData: {
                routesInfo: {
                    airportId: null,
                    routesName: "",
                    routesStartPoint: "",
                    routesEndPoint: "",
                    routesUrl:"",
                    routesId: "",
                    routeMileage: 0
                },
                routesConfig:{
                    flyToWaylineMode: "safely",
                    finishAction: "goHome",
                    exitOnRCLost: "executeLostAction",
                    executeRCLostAction: "goBack",
                    takeOffSecurityHeight: 100,
                    globalTransitionalSpeed: 5,
                    droneEnumValue: 67,
                    droneSubEnumValue: 0,
                    autoFlightSpeed: 5,
                    globalHeight: 100,
                    gimbalPitchMode: "usePointSetting",
                    waypointHeadingMode: "followWayline",
                    waypointHeadingPathMode: "followBadArc",
                    globalWaypointTurnMode: "toPointAndStopWithDiscontinuityCurvature"
                },
                routesPointList:[]
            },
            routesPointObj:{
                coordinates: "", //点位经纬度
                index: 0,
                useGlobalHeight: 0,
                ellipsoidHeight: 150, // 点位海拔高度
                height: 99, // 离地高度
                groundHeight: 50, // 地面海拔高度
                useGlobalSpeed: 1,
                waypointSpeed: 5,
                gimbalPitchAngle: -30,
                executeHeight: 88,
                useGlobalHeadingParam: 1,
                waypointHeadingMode: "followWayline",
                waypointHeadingPathMode: "followBadArc",
                routesPointActionList: [
                    // {
                    //     index: 0,
                    //     actionActuatorFunc: "takePhoto",
                    //     value: "",
                    //     activeName: ""
                    // }
                ]
            },
            waypointHeadingModeOption: [ //无人机偏航角
                {label: "沿航线方向", value: "followWayline"},
                {label: "手动控制", value: "manually"},
                {label: "锁定当前偏航角", value: "fixed"},
            ],
            finishActionOption: [
                {label: "自动返航", value: "goHome"},
                {label: "原地降落", value: "autoLand"}
            ],
            droneSubEnumValueOption: [
                {label: "M40", value: 0},
                {label: "M40T", value: 1}
            ],
            routesPointActionListOption: [
                {label: "拍照", value: "takePhoto"},
                {label: "开始录像", value: "startRecord"},
                {label: "结束录像", value: "stopRecord"},
                {label: "相机变焦", value: "zoom"},
                {label: "飞行器偏航", value: "rotateYaw"},
                {label: "悬停等待", value: "hover"},
                {label: "开始等时间隔拍照", value: "multipleTiming"},
                {label: "开始等距间隔拍照", value: "multipleDistance"},
                {label: "结束间隔拍照", value: "multipleStop"},
            ],
            isDropLine: true, // 航线设置与航点设置切换
            markerList: [], // 航点对象
            lineIDList: [], // 航线ID
            icon1: null, // 绿
            icon2: null, //蓝
            icon3: null, //黄
            timestamp: null, // 节流时间戳，公用
            contextMenu: null, // 航点删除菜单
            lineCanvas:null, // 航线canvas对象
            lineCustomLayer: null, // 航线图层对象
            dragOptions:{
                animation: 120,
                scroll: true,
                group: 'sortlist',
                ghostClass: 'ghost-style'
            },
            addPointBtnObj: null, // 航线上的加号图标
            routesLength: 0, // 航线总长度
            isScreenshot: false, // 是否截图完成
            saveTimer: null,
            isAddress1: false,
            isAddress2: false,
            isLoading: false,
            isHoverIndex: null, // 当前悬停点位
            leftDownFlag: false, // 鼠标左键是否按下
            activePointDrag: { // 当前拖拽点位下标
                index: null, // 下标
                type: null, // 类型top空中点，bottom地面点
            },
            showDeleteBtn: false, // 右键点击点位显示删除按钮
            deleteBtnTop: 0, // 右键点击点位显示删除按钮位置
            deleteBtnLeft: 0, // 右键点击点位显示删除按钮位置
            deletePointIndex: null, // 选中删除的index
            rotateYawIndex: null, // 当前编辑无人机偏航角下标
            addMarkerId: null, // 地图上添加按钮标注id
            mouse_move_line_index: null, // 鼠标在那条线上
        };
    },
    created() {
        
    },
    mounted() {
        // 禁止右键
        window.oncontextmenu=function(){return false;}  

        if(this.$route.params.id == 0) {
            this.fromData.routesInfo.routesName = this.$route.query.routesName
            this.initMap();
            // 新增模式下也加载机场列表
            this.loadAirportList();
        } else {
            this.getRoutesDet()
        }
    },
    methods: {
        // 详情加载
        getRoutesDet(){
            routesDet(this.$route.params.id).then(res => {
                console.log("航线信息", res)
                this.fromData = res.data
                // 从 routesInfo 中获取 isImport 值
                this.fromData.isImport = res.data.routesInfo.isImport
                console.log("this.fromData", this.fromData);
                console.log("isImport值:", this.fromData.isImport);

                // 确保航点列表存在且有数据时才初始化 routesPointObj
                if(this.fromData.routesPointList && this.fromData.routesPointList.length > 0) {
                    this.routesPointObj = this.fromData.routesPointList[0]
                    // 确保每个航点都有 routesPointActionList 属性
                    this.fromData.routesPointList.forEach(point => {
                        if(!point.routesPointActionList) {
                            point.routesPointActionList = []
                        }
                    })
                }
                this.initMap();
                // 加载机场列表并在地图上标注
                this.loadAirportList();
            })
        },
        // 加载机场列表并在地图上标注
        loadAirportList() {
            console.log('开始加载机场列表...');

            // 调用真实的机场列表接口
            listAirport({ pageNum: 1, pageSize: 999 }).then(response => {
                console.log('机场列表接口响应:', response);
                this.airportList = response.rows || [];

                // 延迟确保地图完全加载后再添加标记
                setTimeout(() => {
                    this.addAirportMarkersToMap();
                }, 1000);

                console.log('机场列表加载完成:', this.airportList);
            }).catch(error => {
                console.error('加载机场列表失败:', error);
            });
        },


        // 在地图上添加机场标记
        addAirportMarkersToMap() {
            if (!this.viewer || !this.airportList.length) {
                console.log('地图未初始化或机场列表为空');
                return;
            }

            // 清除之前的机场标记
            this.clearAirportMarkers();

            this.airportList.forEach((airport, index) => {
                // 验证经纬度数据
                if (!airport.longitude || !airport.latitude) {
                    console.warn(`机场 ${airport.airportName} 缺少经纬度数据`);
                    return;
                }

                // 参考 drawMarker 方法创建机场标记
                const airportEntity = this.viewer.entities.add({
                    position: Cesium.Cartesian3.fromDegrees(
                        parseFloat(airport.longitude),
                        parseFloat(airport.latitude),
                        100 // 添加高度，避免贴地问题
                    ),
                    billboard: {
                        image: require("@/assets/images/mapicon/image.png"),
                        show: true,
                        width: 40,
                        height: 40,
                        verticalOrigin: Cesium.VerticalOrigin.BOTTOM
                    },
                    label: {
                        text: airport.airportName || `机场${index + 1}`,
                        font: '18px sans-serif',
                        style: Cesium.LabelStyle.FILL_AND_OUTLINE,
                        pixelOffset: new Cesium.Cartesian2(0, -15),
                        fillColor: Cesium.Color.fromCssColorString('#03d791'),
                        outlineWidth: 3,
                        outlineColor: Cesium.Color.fromCssColorString('#000000'),
                        verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
                        horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
                        disableDepthTestDistance: Number.POSITIVE_INFINITY
                    }
                });

                // 保存标记引用
                this.airportMarkers.push(airportEntity);
            });

            console.log(`已在地图上添加 ${this.airportMarkers.length} 个机场标记`);
        },


        // 清除机场标记
        clearAirportMarkers() {
            if (this.airportMarkers && this.airportMarkers.length > 0) {
                this.airportMarkers.forEach(marker => {
                    if (this.viewer && this.viewer.entities) {
                        this.viewer.entities.remove(marker);
                    }
                });
            }
            this.airportMarkers = [];
        },
        // 截图地图
        getMapImg(){
            let _this = this
            window.pageYOffset = 0;
            document.documentElement.scrollTop = 0;
            document.body.scrollTop = 0;
            html2canvas(
                document.getElementById("uvaline-map"),
                {
                    backgroundColor: null,//画出来的图片有白色的边框,不要可设置背景为透明色（null）
                    useCORS: true,//支持图片跨域
                    scale: 0.5,//设置放大的倍数
                }
            ).then(canvas => {
                // console.log("canvas",canvas)
                canvas.toBlob(blob => {
                    let fd = new FormData()
                    fd.append("file", blob)
                    uploadPlanImg(fd, _this.uploadProgress).then(res => {
                        // console.log("res",res)
                        _this.fromData.routesInfo.routesUrl = res.url
                        _this.isScreenshot = true
                    })
                })
            })
        },
        uploadProgress(e){
            console.log("进度",e)
        },
        // 保存
        addRouteLine(){
            let _this = this
            console.log("this.fromData",this.fromData)
            if(this.fromData.routesPointList.length<2) {
                this.$modal.msgWarning("最少添加两个航点！");
                return ;
            }
            this.isLoading = true
            this.getMapImg()

            getWgs84togcj02(this.fromData.routesPointList[0].coordinates).then(res => {
                console.log(res)
                if(res.data.infocode == "10000") {
                    getRegeo({location: res.data.locations}).then(ress => {
                        console.log(ress)
                        if(ress.data.infocode == "10000") {
                            _this.fromData.routesInfo.routesStartPoint = ress.data.regeocode.formatted_address
                            _this.isAddress1 = true
                        } else {
                            _this.fromData.routesInfo.routesStartPoint = "未获取到地址"
                            _this.isAddress1 = true
                        }
                    })
                } else {
                    _this.fromData.routesInfo.routesStartPoint = "未获取到地址"
                     _this.isAddress1 = true
                }
            })

            getWgs84togcj02(this.fromData.routesPointList[this.fromData.routesPointList.length-1].coordinates).then(res => {
                console.log(res)
                if(res.data.infocode == "10000") {
                    getRegeo({location: res.data.locations}).then(ress => {
                        console.log(ress)
                        if(ress.data.infocode == "10000") {
                            _this.fromData.routesInfo.routesEndPoint = ress.data.regeocode.formatted_address
                            _this.isAddress2 = true
                        } else {
                            _this.fromData.routesInfo.routesEndPoint = "未获取到地址"
                            _this.isAddress2 = true
                        }
                    })
                } else {
                    _this.fromData.routesInfo.routesEndPoint = "未获取到地址"
                     _this.isAddress2 = true
                }
            })

            _this.saveTimer = setInterval(() => {
                if(_this.isScreenshot && _this.isAddress1 && _this.isAddress2) {
                    clearInterval(_this.saveTimer)
                    _this.saveTimer = null
                    if(this.fromData.routesInfo.routesId == "") {
                        addNotAirportRoutes(this.fromData).then(res => {
                            console.log("航线保存",res)
                            this.$modal.msgSuccess("保存成功");
                            _this.isLoading = false
                            _this.$router.go(-1);
                        }).catch(err => {
                            console.log(err)
                            _this.isLoading = false
                        })
                    } else {
                        putNotAirportRoutes(this.fromData).then(res => {
                            console.log("航线保存",res)
                            this.$modal.msgSuccess("保存成功");
                            _this.isLoading = false
                            _this.$router.go(-1);
                        }).catch(err => {
                            console.log(err)
                            _this.isLoading = false
                        })
                    }
                }
            },1000)

        },
        deleteAction(data){
            this.routesPointObj.routesPointActionList.splice(data.index,1)
            this.routesPointObj.routesPointActionList.forEach((item,index) => {
                item.index = index
            })
            this.detectionFrustum()
        },
        actionDraggable(e){
            this.routesPointObj.routesPointActionList.forEach((item,index) => {
                item.index = index
            })
        },
        actionMax(actionActuatorFunc){
            switch(actionActuatorFunc) {
                case "zoom":
                    return 200;
                case "rotateYaw":
                    return 180;
                case "hover":
                    return 30;
                case "multipleTiming":
                    return 30;
                case "multipleDistance":
                    return 100;
                default: break;
            }
        },
        needSlider(actionActuatorFunc) {
            return actionActuatorFunc === 'zoom' ||
                   actionActuatorFunc === 'rotateYaw' ||
                   actionActuatorFunc === 'hover' ||
                   actionActuatorFunc === 'multipleTiming' ||
                   actionActuatorFunc === 'multipleDistance';
        },
        getActionLabel(actionActuatorFunc) {
            const action = this.routesPointActionListOption.find(item => item.value === actionActuatorFunc);
            return action ? action.label : '未知动作';
        },
        // 修改航点高度加减
        editPointHeight(character) {
            if(character == '-') {
                this.routesPointObj.height--
            } else {
                this.routesPointObj.height++
            }
            this.routesPointObj.ellipsoidHeight = this.routesPointObj.height + this.routesPointObj.groundHeight
            this.routesPointObj.executeHeight = this.routesPointObj.height
            this.routesPointObj.useGlobalHeight = 0 // 取消使用全局高度
            this.drawAllMarker()
            this.updateFrustum()
        },
        // 修改航点高度进度条
        pointHeightChange(val){
            let _this = this
            console.log(val)
            this.routesPointObj.ellipsoidHeight = this.routesPointObj.height + this.routesPointObj.groundHeight
            this.routesPointObj.executeHeight = this.routesPointObj.height
            this.routesPointObj.useGlobalHeight = 0
            this.drawAllMarker()
            this.updateFrustum()
        },
        // 修改全局高度加减
        editGlobalHeight(character){
            if(character == '-') {
                this.fromData.routesConfig.globalHeight--
            } else {
                this.fromData.routesConfig.globalHeight++
            }
            this.fromData.routesPointList.forEach(item => {
                if(item.useGlobalHeight == 1) {
                    item.ellipsoidHeight = this.fromData.routesConfig.globalHeight + item.groundHeight
                    item.executeHeight = item.height
                    item.height = this.fromData.routesConfig.globalHeight
                }
            })
            this.routesPointObj = this.fromData.routesPointList[this.routesPointObj.index]
            this.drawAllMarker()
        },
        // 修改全局高度进度条
        globalHeightChange(val){
            this.fromData.routesPointList.forEach(item => {
                if(item.useGlobalHeight == 1) {
                    item.ellipsoidHeight = this.fromData.routesConfig.globalHeight + item.groundHeight
                    item.executeHeight = item.height
                    item.height = this.fromData.routesConfig.globalHeight
                }
            })
            this.routesPointObj = this.fromData.routesPointList[this.routesPointObj.index]
            this.drawAllMarker()
        },
        // 航线航点切换
        switchDropLine(){
            if(this.fromData.routesPointList.length != 0) {
                // 确保 routesPointObj 正确初始化
                if(!this.routesPointObj || this.routesPointObj.coordinates === "") {
                    this.routesPointObj = this.fromData.routesPointList[0]
                }
                // 确保 routesPointActionList 存在
                if(!this.routesPointObj.routesPointActionList) {
                    this.routesPointObj.routesPointActionList = []
                }
                this.isDropLine = false
            } else {
                this.$modal.msg("请添加航点！")
            }
        },
        // 切换航点
        switchPoint(character){
            if(character == '-') {
                if(this.routesPointObj.index > 0) {
                    this.routesPointObj = this.fromData.routesPointList[this.routesPointObj.index - 1]
                }
            } else {
                if(this.routesPointObj.index < this.fromData.routesPointList.length - 1) {
                    this.routesPointObj = this.fromData.routesPointList[this.routesPointObj.index + 1]
                }
            }
            this.detectionFrustum()
            this.drawAllMarker()
        },
        // 切换点时检测是否需要绘画视锥体
        detectionFrustum(){
            let index = this.routesPointObj.routesPointActionList.findIndex(item => item.actionActuatorFunc == 'rotateYaw')
            if(index != -1) {
                this.rotateYawIndex = index
                if(this.Frustum) {
                    let lonlat = this.routesPointObj.coordinates.split(',')
                    let origin = Cesium.Cartesian3.fromDegrees(lonlat[0],lonlat[1], this.routesPointObj.ellipsoidHeight);
                    let headingPitchRoll = new Cesium.HeadingPitchRoll((90 + this.routesPointObj.routesPointActionList[this.rotateYawIndex].value ) * Math.PI / 180, (90 - this.routesPointObj.gimbalPitchAngle) * Math.PI / 180, 0 * Math.PI / 180)
                    let orientation = Cesium.Transforms.headingPitchRollQuaternion(origin,headingPitchRoll)
                    this.Frustum.update(origin,orientation)
                } else {
                    let lonlat = this.routesPointObj.coordinates.split(',')
                    this.createFrustum(lonlat[0],lonlat[1],this.routesPointObj.ellipsoidHeight, (90 + this.routesPointObj.routesPointActionList[this.rotateYawIndex].value), (90 - this.routesPointObj.gimbalPitchAngle), 0)
                }
            } else {
                if(this.Frustum) {
                    this.Frustum.clear()
                    this.Frustum = null
                }
            }
        },
        // 修改视锥体姿态
        updateFrustum(){
            if(this.Frustum) {
                let lonlat = this.routesPointObj.coordinates.split(',')
                let origin = Cesium.Cartesian3.fromDegrees(lonlat[0],lonlat[1], this.routesPointObj.ellipsoidHeight);
                let headingPitchRoll = new Cesium.HeadingPitchRoll((90 + this.routesPointObj.routesPointActionList[this.rotateYawIndex].value ) * Math.PI / 180, (90 - this.routesPointObj.gimbalPitchAngle) * Math.PI / 180, 0 * Math.PI / 180)
                let orientation = Cesium.Transforms.headingPitchRollQuaternion(origin,headingPitchRoll)
                this.Frustum.update(origin,orientation)
            }
        },
        // 修改云台俯仰角
        setPointGimbalPitchAngle(val){
            this.updateFrustum()
        },
        // 按钮加减修改云台俯仰角
        setPointGimbalPitchAngle1(type){
            if(type == '-') {
                this.routesPointObj.gimbalPitchAngle--
            } else {
                this.routesPointObj.gimbalPitchAngle++
            }
            this.updateFrustum()
        },
        // 编辑动作参数值
        editActionActuatorFunc(data){
            if(data.actionActuatorFunc == "rotateYaw") {
                this.rotateYawIndex = data.index
                this.updateFrustum()
            }
        },
        // 按钮编辑动作参数值
        editActionActuatorFunc1(data,type){
            if(type == '-') {
                this.routesPointObj.routesPointActionList[data.index].value--
            } else {
                this.routesPointObj.routesPointActionList[data.index].value++
            }
            if(data.actionActuatorFunc == "rotateYaw") {
                this.rotateYawIndex = data.index
                this.updateFrustum()
            }
        },
        // 添加动作
        selectAction(data){
            console.log(data)
            let obj = {
                index: this.routesPointObj.routesPointActionList.length,
                actionActuatorFunc: data,
                value: "",
            }
            switch(data) {
                case "takePhoto":
                    break;
                case "startRecord":
                    break;
                case "stopRecord":
                    break;
                case "zoom":
                    obj.value = 2
                    break;
                case "rotateYaw":
                    obj.value = 0
                    break;
                case "hover":
                    obj.value = 15
                    break;
                case "multipleTiming":
                    obj.value = 15
                    break;
                case "multipleDistance":
                    obj.value = 50
                    break;
                case "multipleStop":
                    break;
            }
            this.routesPointObj.routesPointActionList.push(obj)
            this.rotateYawIndex = this.routesPointObj.routesPointActionList.length - 1
            if(data == "rotateYaw") {
                if(!this.Frustum) {
                    let lonlat = this.routesPointObj.coordinates.split(',')
                    this.createFrustum(lonlat[0],lonlat[1],this.routesPointObj.ellipsoidHeight, (90 + this.routesPointObj.routesPointActionList[this.rotateYawIndex].value), (90 - this.routesPointObj.gimbalPitchAngle), 0)
                } else {
                    let lonlat = this.routesPointObj.coordinates.split(',')
                    let origin = Cesium.Cartesian3.fromDegrees(lonlat[0],lonlat[1], this.routesPointObj.ellipsoidHeight);
                    let headingPitchRoll = new Cesium.HeadingPitchRoll((90 + this.routesPointObj.routesPointActionList[this.rotateYawIndex].value ) * Math.PI / 180, (90 - this.routesPointObj.gimbalPitchAngle) * Math.PI / 180, 0 * Math.PI / 180)
                    let orientation = Cesium.Transforms.headingPitchRollQuaternion(origin,headingPitchRoll)
                    this.Frustum.update(origin,orientation)
                }
            }
        },
        //修改航线名称
        editLineName(){
            this.$prompt('请输入目标点名称', '重命名', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                inputValue: this.fromData.routesInfo.routesName
            }).then(({ value }) => {
                this.fromData.routesInfo.routesName = value;
            }).catch(() => {});
        },
        // 初始化地图
        initMap() {
            let _this = this;
            Cesium.Ion.defaultAccessToken = cesiumToken;
            this.viewer = new Cesium.Viewer("uvaline-map", {
                // imageryProviderViewModels: [tiandiImgModel,tiandiVecModel],
                geocoder: false, //搜索框
                animation: false, //左下角的动画控件的显示
                shouldAnimate: false, //控制模型动画
                timeline: false, //底部的时间轴
                fullscreenButton: false, //右下角的全屏按钮
                selectionIndicator: true, //选择指示器
                homeButton: false,
                infoBox: false, //信息面板
                baseLayerPicker: false, //图层选择按钮
                navigationHelpButton: false, //右上角的帮助按钮
                sceneModePicker: false, //3d/2d 模式切换按钮
                terrainProvider: Cesium.createWorldTerrain({
                    requestWaterMask : true,
                    requestVertexNormals : true
                }), // 提供地形 使用Cesium在线Ion地形
                skyBox: false,
                scene3DOnly: true,
                "contextOptions": {
                    "webgl": {
                        "alpha": true,
                        "depth": false,
                        "stencil": true,
                        "antialias": true,
                        "premultipliedAlpha": true,
                        "preserveDrawingBuffer": true,
                        "failIfMajorPerformanceCaveat": true
                    },
                    "allowTextureFilterAnisotropic": true
                },

                // shouldAnimate: true, // 表示实体是运行状态
            });

            this.viewer.imageryLayers.addImageryProvider(tiandiImg_w, 10)
            this.viewer.imageryLayers.addImageryProvider(tiandiCia_w, 10)

            this.viewer.scene.screenSpaceCameraController.zoomEventTypes = [ Cesium.CameraEventType.WHEEL ]
            // 设置鼠标右键拖动地图, 允许用户在3D和2.5D模式下倾斜，或者在2D模式下旋转的输入
            this.viewer.scene.screenSpaceCameraController.tiltEventTypes = [ Cesium.CameraEventType.RIGHT_DRAG ]
            this.viewer.scene.screenSpaceCameraController.minimumZoomDistance = 0.8
            // 开启深度检测
            this.viewer.scene.globe.depthTestAgainstTerrain = true;
            //隐藏logo版
            this.viewer._cesiumWidget._creditContainer.style.display = "none";

            if(this.fromData.routesPointList.length != 0) {
                let lonlat = this.fromData.routesPointList[0].coordinates.split(',')
                _this.viewer.camera.setView({
                    destination: Cesium.Cartesian3.fromDegrees(lonlat[0],lonlat[1], 1500.0),
                })
            } else {
                // window._AMapSecurityConfig = {
                //     securityJsCode: amapSecretkey
                // }
                // AMapLoader.load({
                //     key: amapKey,
                //     version: "2.0",
                //     plugins: [
                //         "AMap.Geolocation"
                //     ],
                // }).then((AMap) => {
                //     var geolocation = new AMap.Geolocation({
                //         enableHighAccuracy: true,//是否使用高精度定位，默认:true
                //         timeout: 10000,          //超过10秒后停止定位，默认：无穷大
                //         maximumAge: 0,           //定位结果缓存0毫秒，默认：0
                //         convert: false,
                //         showMarker: false
                //     });
                //     geolocation.getCurrentPosition((status, result) => {
                //         console.log("定位状态", status)
                        
                //         console.log("定位信息", result)
                //         if(status == 'complete' && result.code == 0) {
                //             _this.viewer.camera.setView({
                //                 destination: Cesium.Cartesian3.fromDegrees(result.position.lng,result.position.lat, 1500)
                //             })
                //         }
                //     })

                // }).catch((e) => {
                //     console.log(e);
                // })
                // 定位到中国地图
                _this.viewer.camera.setView({
                    destination: Cesium.Cartesian3.fromDegrees(109.364933,31.532234, 2457340.0)
                })
                // 浏览器定位
                // if(navigator.geolocation) {
                //     console.log("进入浏览器定位")
                //     navigator.geolocation.getCurrentPosition(position => {
                //         console.log("获取到位置",position)
                //         _this.viewer.camera.setView({
                //             destination: Cesium.Cartesian3.fromDegrees(position.coords.longitude,position.coords.latitude, 1500.0),
                //         })
                //     }, error => {
                //         console.log("定位error",error)
                //         switch (error.code) {
                //             case error.PERMISSION_DENIED:
                //                 console.log("用户拒绝对获取地理位置的请求。")
                //                 break;
                //             case error.POSITION_UNAVAILABLE:
                //                 console.log("位置信息是不可用的。")
                //                 break;
                //             case error.TIMEOUT:
                //                 console.log("请求用户地理位置超时。")
                //                 break;
                //             case error.UNKNOWN_ERROR:
                //                 console.log("未知错误。")
                //                 break;
                //         }
                //     })
                // } else {
                    // console.log("不支持")
                    getCurrentLocation().then(res => {
                        console.log("当前位置信息：", res)
                        if(res.data.info == "OK") {
                            let data = res.data.rectangle.split(";")
                            let loglat1 = data[0].split(",")
                            let loglat2 = data[1].split(",")
                            let loglat3 = gcj02towgs84(loglat1[0],loglat1[1])
                            let loglat4 = gcj02towgs84(loglat2[0],loglat2[1])
                            _this.viewer.camera.setView({
                                destination : Cesium.Rectangle.fromDegrees(loglat3[0],loglat3[1],loglat4[0],loglat4[1])
                            })
                        } else {
                            _this.viewer.camera.setView({
                                destination: Cesium.Cartesian3.fromDegrees(109.364933,31.532234, 2457340.0)
                            })
                        }
                    })
                // }
            }
            // 加载所有航线
            _this.drawAllMarker()
            // 检测是否需要加载或清除视锥体
            _this.detectionFrustum()
            // 事件实例
            let handler = new Cesium.ScreenSpaceEventHandler(_this.viewer.scene.canvas);
            // 左键点击事件
            handler.setInputAction(event => {
                // console.log("event",event)
                let pick = _this.viewer.scene.pick(event.position)
                // console.log("pick",pick)
                if (pick && pick.id) {
                    _this.viewer._selectedEntity = [] //去除左击之后出现选中的绿框
                    let index = _this.markerList.findIndex(item => item.topMarker == pick.id.id || item.bottomMarker == pick.id.id)
                    // console.log("index",index)
                    if(index != -1) {
                        let entity1 = _this.viewer.entities.getById(_this.markerList[_this.routesPointObj.index].topMarker);
                        entity1.billboard.image = require("@/assets/images/mapicon/airport2.png")
                        _this.routesPointObj = _this.fromData.routesPointList[index]
                        let entity2 = _this.viewer.entities.getById(_this.markerList[_this.routesPointObj.index].topMarker);
                        entity2.billboard.image = require("@/assets/images/mapicon/airport1.png")
                        _this.switchDropLine()
                    } else if(_this.addMarkerId == pick.id.id) {
                        console.log("点击到加号")
                        let lotlat1 = _this.fromData.routesPointList[_this.mouse_move_line_index].coordinates.split(',')
                        let lotlat2 = _this.fromData.routesPointList[_this.mouse_move_line_index + 1].coordinates.split(',')
                        let lotlat =  [(Number(lotlat1[0]) + Number(lotlat2[0])) / 2, (Number(lotlat1[1]) + Number(lotlat2[1])) / 2]
                        _this.routesPointObj = {
                            coordinates: lotlat[0] + ',' + lotlat[1],
                            index: _this.mouse_move_line_index + 1,
                            useGlobalHeight: 0,
                            ellipsoidHeight: (_this.fromData.routesPointList[_this.mouse_move_line_index].ellipsoidHeight + _this.fromData.routesPointList[_this.mouse_move_line_index + 1].ellipsoidHeight)/2,
                            height: (_this.fromData.routesPointList[_this.mouse_move_line_index].height + _this.fromData.routesPointList[_this.mouse_move_line_index + 1].height)/2,
                            groundHeight: 0,
                            useGlobalSpeed: 1,
                            waypointSpeed: _this.fromData.routesConfig.autoFlightSpeed,
                            gimbalPitchAngle: 0,
                            executeHeight: (_this.fromData.routesPointList[_this.mouse_move_line_index].height + _this.fromData.routesPointList[_this.mouse_move_line_index + 1].height)/2,
                            useGlobalHeadingParam: 1,
                            waypointHeadingMode: "followWayline",
                            waypointHeadingPathMode: "followBadArc",
                            routesPointActionList: [],
                        }
                        _this.fromData.routesPointList.splice(_this.mouse_move_line_index + 1, 0, _this.routesPointObj)
                        _this.drawAllMarker()
                        const terrainProvider = Cesium.createWorldTerrain();
                        const positions = [
                            Cesium.Cartographic.fromDegrees(lotlat[0],lotlat[1])
                        ];
                        const promise = Cesium.sampleTerrainMostDetailed(terrainProvider, positions);
                        Promise.resolve(promise).then(function(updatedPositions) {
                            console.log("updatedPositions",updatedPositions[0].height)
                            _this.routesPointObj.groundHeight = updatedPositions[0].height
                            _this.fromData.routesPointList[_this.mouse_move_line_index + 1].groundHeight = updatedPositions[0].height
                        });
                        if(_this.addMarkerId) {
                            let entityAdd = _this.viewer.entities.getById(_this.addMarkerId);
                            entityAdd.show  = false
                        }
                        _this.switchDropLine()
                    }
                } else {
                    let ray = _this.viewer.camera.getPickRay(event.position);
                    let cartesian = _this.viewer.scene.globe.pick(ray, _this.viewer.scene);
                    // console.log("世界坐标",cartesian)
                    let cartographic = Cesium.Cartographic.fromCartesian(cartesian);
                    let lng = Cesium.Math.toDegrees(cartographic.longitude); // 经度
                    let lat = Cesium.Math.toDegrees(cartographic.latitude); // 纬度
                    let alt = cartographic.height; // 高度
                    let coordinate = {
                        longitude: Number(lng.toFixed(6)),
                        latitude: Number(lat.toFixed(6)),
                        altitude: Number(alt.toFixed(2))
                    };
                    console.log("获取加载地形后的经纬度(弧度)和高程",coordinate);
                    // console.log("相机当前属性：",_this.getcameraPosInfo())
                    _this.routesPointObj = {
                        coordinates: coordinate.longitude + ',' + coordinate.latitude,
                        index: this.fromData.routesPointList.length,
                        useGlobalHeight: 1,
                        ellipsoidHeight: this.fromData.routesConfig.globalHeight + coordinate.altitude,
                        height: this.fromData.routesConfig.globalHeight,
                        groundHeight: coordinate.altitude,
                        useGlobalSpeed: 1,
                        waypointSpeed: this.fromData.routesConfig.autoFlightSpeed,
                        gimbalPitchAngle: 0,
                        executeHeight: this.fromData.routesConfig.globalHeight,
                        useGlobalHeadingParam: 1,
                        waypointHeadingMode: "followWayline",
                        waypointHeadingPathMode: "followBadArc",
                        routesPointActionList: [],
                    }
                    _this.fromData.routesPointList.push(_this.routesPointObj)
                    _this.drawAllMarker()
                    _this.switchDropLine()
                }
                // 检测是否需要加载或清除视锥体
                _this.detectionFrustum()
            }, Cesium.ScreenSpaceEventType.LEFT_CLICK);
            // 鼠标悬停事件
            handler.setInputAction( _this.viewer_mpuse_move, Cesium.ScreenSpaceEventType.MOUSE_MOVE)
            // 左键按下事件
            handler.setInputAction(e => {
                // console.log("左键按下事件",e)
                _this.showDeleteBtn = false
                _this.deletePointIndex = null
                _this.viewer.scene.screenSpaceCameraController.enableRotate = true;
                _this.leftDownFlag = true
                let pick = _this.viewer.scene.pick(e.position)
                if(pick) {
                    let index1 = _this.markerList.findIndex(item => item.topMarker == pick.id.id )
                    let index2 = _this.markerList.findIndex(item => item.bottomMarker == pick.id.id)
                    if((index1 != -1 || index2 != -1) && (index1 == _this.routesPointObj.index || index2 == _this.routesPointObj.index)) {
                        _this.viewer.scene.screenSpaceCameraController.enableRotate = false;
                        // 空中点
                        if(index1 != -1) {
                            _this.activePointDrag.index = index1
                            _this.activePointDrag.type = 'top'
                        }
                        // 地面点
                        if(index2 != -1) {
                            _this.activePointDrag.index = index2
                            _this.activePointDrag.type = 'bottom'
                        }
                    }
                    
                } 
                // handler.removeInputAction(Cesium.ScreenSpaceEventType.MOUSE_MOVE)
            }, Cesium.ScreenSpaceEventType.LEFT_DOWN)
            // 左键松开事件
            handler.setInputAction(e => {
                // console.log("左键松开事件",e)
                _this.leftDownFlag = false
                _this.viewer.scene.screenSpaceCameraController.enableRotate = true;
                _this.activePointDrag.index = null
                _this.activePointDrag.type = null
            }, Cesium.ScreenSpaceEventType.LEFT_UP)
            // 右键点击事件
            handler.setInputAction(e => {
                console.log("右键点击事件",e)
                let pick = _this.viewer.scene.pick(e.position)
                if(pick && pick.id) {
                    let index = _this.markerList.findIndex(item => item.topMarker == pick.id.id || item.bottomMarker == pick.id.id)
                    if(index != -1) {
                        _this.viewer.scene.screenSpaceCameraController.enableRotate = false;
                        _this.showDeleteBtn = true
                        _this.deleteBtnTop = e.position.y 
                        _this.deleteBtnLeft = e.position.x + 30
                        _this.deletePointIndex = index
                    }
                } else {
                    _this.showDeleteBtn = false
                    _this.deletePointIndex = null
                    _this.viewer.scene.screenSpaceCameraController.enableRotate = true;
                }
            },Cesium.ScreenSpaceEventType.RIGHT_CLICK)


        },
        // 鼠标MOUSE_MOVE事件
        viewer_mpuse_move(e){
            let _this = this
            if(_this.leftDownFlag && _this.activePointDrag.index != null) {
                if(_this.activePointDrag.type == 'top') {
                    let loglat = _this.routesPointObj.coordinates.split(',')
                    // 获取点位世界坐标
                    let worldPosition = Cesium.Cartesian3.fromDegrees(loglat[0], loglat[1], _this.routesPointObj.groundHeight)
                    // 获取点位屏幕
                    let Cartesian = Cesium.SceneTransforms.wgs84ToWindowCoordinates(_this.viewer.scene, worldPosition);
                    // 计算偏移量
                    Cartesian.x  +=  (e.endPosition.x - e.startPosition.x)
                    Cartesian.y  +=  (e.endPosition.y - e.startPosition.y)
                    let ray = _this.viewer.camera.getPickRay(Cartesian);
                    let cartesian = _this.viewer.scene.globe.pick(ray, _this.viewer.scene);
                    
                    // console.log("世界坐标",cartesian)
                    let cartographic = Cesium.Cartographic.fromCartesian(cartesian);
                    let lng = Cesium.Math.toDegrees(cartographic.longitude); // 经度
                    let lat = Cesium.Math.toDegrees(cartographic.latitude); // 纬度
                    let alt = cartographic.height; // 高度
                    let coordinate = {
                        longitude: Number(lng.toFixed(6)),
                        latitude: Number(lat.toFixed(6)),
                        altitude: Number(alt.toFixed(2))
                    };

                    _this.routesPointObj.coordinates = coordinate.longitude + ',' + coordinate.latitude
                    _this.routesPointObj.ellipsoidHeight = _this.routesPointObj.height + coordinate.altitude
                    _this.routesPointObj.groundHeight = coordinate.altitude
                    _this.routesPointObj.executeHeight = _this.routesPointObj.height
                    var entityTop = _this.viewer.entities.getById(_this.markerList[_this.activePointDrag.index].topMarker);
                    var bottomMarker = _this.viewer.entities.getById(_this.markerList[_this.activePointDrag.index].bottomMarker);
                    var line = _this.viewer.entities.getById(_this.markerList[_this.activePointDrag.index].line);
                    let position1 = Cesium.Cartesian3.fromDegrees(coordinate.longitude, coordinate.latitude, _this.routesPointObj.ellipsoidHeight)
                    let position2 = Cesium.Cartesian3.fromDegrees(coordinate.longitude, coordinate.latitude)
                    entityTop.position = position1
                    bottomMarker.position = position2
                    line.polyline.positions = [position1,position2]
                    _this.drawLine()
                } else {
                    let ray = _this.viewer.camera.getPickRay(e.endPosition);
                    let cartesian = _this.viewer.scene.globe.pick(ray, _this.viewer.scene);
                    // console.log("世界坐标",cartesian)
                    let cartographic = Cesium.Cartographic.fromCartesian(cartesian);
                    let lng = Cesium.Math.toDegrees(cartographic.longitude); // 经度
                    let lat = Cesium.Math.toDegrees(cartographic.latitude); // 纬度
                    let alt = cartographic.height; // 高度
                    let coordinate = {
                        longitude: Number(lng.toFixed(6)),
                        latitude: Number(lat.toFixed(6)),
                        altitude: Number(alt.toFixed(2))
                    };
                    _this.routesPointObj.coordinates = coordinate.longitude + ',' + coordinate.latitude
                    _this.routesPointObj.ellipsoidHeight = _this.routesPointObj.height + coordinate.altitude
                    _this.routesPointObj.groundHeight = coordinate.altitude
                    _this.routesPointObj.executeHeight = _this.routesPointObj.height
                    var entityTop = _this.viewer.entities.getById(_this.markerList[_this.activePointDrag.index].topMarker);
                    var bottomMarker = _this.viewer.entities.getById(_this.markerList[_this.activePointDrag.index].bottomMarker);
                    var line = _this.viewer.entities.getById(_this.markerList[_this.activePointDrag.index].line);
                    let position1 = Cesium.Cartesian3.fromDegrees(coordinate.longitude, coordinate.latitude, _this.routesPointObj.ellipsoidHeight)
                    let position2 = Cesium.Cartesian3.fromDegrees(coordinate.longitude, coordinate.latitude)
                    entityTop.position = position1
                    bottomMarker.position = position2
                    line.polyline.positions = [position1,position2]
                    _this.drawLine()
                }
                _this.updateFrustum()

                if(_this.addMarkerId) {
                    let entityAdd = _this.viewer.entities.getById(_this.addMarkerId);
                    entityAdd.show  = false
                }
            } else {
                let pick = _this.viewer.scene.pick(e.endPosition)
                // console.log("pick",pick)
                if(pick && pick.id) {
                    _this.viewer._container.style.cursor = "pointer";
                    let index = _this.markerList.findIndex(item => item.topMarker == pick.id.id || item.bottomMarker == pick.id.id)
                    if(index != -1 && index != _this.routesPointObj.index) {
                        _this.isHoverIndex = index
                        let entity = _this.viewer.entities.getById(_this.markerList[index].topMarker);
                        entity.billboard.image = require("@/assets/images/mapicon/airport1.png")
                    } else if(index != -1 && index == _this.routesPointObj.index) {
                        _this.viewer._container.style.cursor = "move";
                    }
                    // 判断鼠标是否在航线上
                    if(index == -1) {
                        let lineIndex = _this.lineIDList.findIndex(item => item == pick.id.id)
                        // console.log("lineIndex",lineIndex)
                        if(lineIndex != -1) {
                            let lotlat1 = _this.fromData.routesPointList[lineIndex].coordinates.split(',')
                            let lotlat2 = _this.fromData.routesPointList[lineIndex + 1].coordinates.split(',')
                            let position = Cesium.Cartesian3.fromDegrees((Number(lotlat1[0]) + Number(lotlat2[0])) / 2, (Number(lotlat1[1]) + Number(lotlat2[1])) / 2, (_this.fromData.routesPointList[lineIndex].ellipsoidHeight + _this.fromData.routesPointList[lineIndex + 1].ellipsoidHeight)/2)
                            _this.mouse_move_line_index = lineIndex
                            if(_this.addMarkerId) {
                                let entityAdd = _this.viewer.entities.getById(_this.addMarkerId);
                                entityAdd.position = position
                                entityAdd.show  = true
                            } else {
                                _this.addMarkerId = createUniqueString()
                                _this.viewer.entities.add({
                                    id: _this.addMarkerId,
                                    position: position,
                                    billboard: {
                                        image: require("@/assets/images/mapicon/add.png"),
                                        show: true,
                                        width: 30, // default: undefined
                                        height: 30, // default: undefined
                                        pixelOffset: new Cesium.Cartesian2(0, 15), // 偏移量
                                        verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
                                        disableDepthTestDistance: Number.POSITIVE_INFINITY, // 被遮挡是否可见（也就是将这个Entity在场景中置顶）
                                    },
                                })
                            }

                            
                        }
                    } else {
                        if(_this.addMarkerId) {
                            let entityAdd = _this.viewer.entities.getById(_this.addMarkerId);
                            entityAdd.show  = false
                        }
                    }
                } else {
                    _this.viewer._container.style.cursor = "default";
                    if(_this.isHoverIndex != null && _this.isHoverIndex != _this.routesPointObj.index) {
                        let entity = _this.viewer.entities.getById(_this.markerList[_this.isHoverIndex].topMarker);
                        entity.billboard.image = require("@/assets/images/mapicon/airport2.png")
                        _this.isHoverIndex = null
                    } else {
                        _this.isHoverIndex = null
                    }

                    if(_this.addMarkerId) {
                        let entityAdd = _this.viewer.entities.getById(_this.addMarkerId);
                        entityAdd.show  = false
                    }
                }
            }
        },
        // 删除航点
        deletePoint(){
            let _this = this
            if(_this.deletePointIndex != null) {
                _this.fromData.routesPointList.splice(_this.deletePointIndex,1)
                _this.routesPointObj = _this.fromData.routesPointList[this.fromData.routesPointList.length - 1]
                _this.showDeleteBtn = false
                _this.deletePointIndex = null
                _this.drawAllMarker()
            }
        },
        // 画点（地面点和空中点对应，并且画虚线） coordinate： {longitude，latitude，altitude}
        drawMarker(data){
            let _this = this
            let loglat = data.coordinates.split(',')
            let position1 = Cesium.Cartesian3.fromDegrees(loglat[0], loglat[1], data.ellipsoidHeight)
            let position2 = Cesium.Cartesian3.fromDegrees(loglat[0], loglat[1])
            // markerList
            let obj = {
                topMarker: createUniqueString(),
                bottomMarker: createUniqueString(),
                line: createUniqueString()
            }
            _this.viewer.entities.add({
                id: obj.topMarker,
                position: position1,
                billboard: {
                    image:_this.routesPointObj.index == data.index ? require("@/assets/images/mapicon/airport1.png") :  require("@/assets/images/mapicon/airport2.png"),
                    show: true,
                    width: 40, // default: undefined
                    height: 40, // default: undefined
                    verticalOrigin: Cesium.VerticalOrigin.BOTTOM
                    // disableDepthTestDistance: Number.POSITIVE_INFINITY, // 被遮挡是否可见（也就是将这个Entity在场景中置顶）
                },
                label: {
                    text: (data.index + 1) + "",
                    font: '16px sans-serif', // 字体大小
                    style: Cesium.LabelStyle.FILL_AND_OUTLINE, // 字体样式
                    pixelOffset: new Cesium.Cartesian2(0, -15), // 偏移量
                    fillColor: Cesium.Color.fromCssColorString('#ffffff'), // 字体填充色
                    outlineWidth: 3, // 字体外圈线宽度（同样也有颜色可设置）
                    outlineColor: Cesium.Color.fromCssColorString('#ffffff'),
                    verticalOrigin: Cesium.VerticalOrigin.BOTTOM, // 垂直位置
                    horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
                    disableDepthTestDistance: Number.POSITIVE_INFINITY,
                    // heightReference: Cesium.HeightReference.CLAMP_TO_GROUND, // 贴地
                },
            })

            _this.viewer.entities.add({
                id: obj.bottomMarker,
                position: position2,
                point: {
                    color: Cesium.Color.WHITE,
                    pixelSize: 10, // 像素点大小
                    // outlineWidth: 2, // 点的外圈线宽度
                    disableDepthTestDistance: Number.POSITIVE_INFINITY,
                    heightReference: Cesium.HeightReference.CLAMP_TO_GROUND, 
                }
            })

            _this.viewer.entities.add({
                id: obj.line,
                name: 'line', 
                polyline: {
                    positions: [position1, position2], // 由点构线
                    width: 2.0, // 线的宽度
                    material: new Cesium.PolylineDashMaterialProperty({
                        color: Cesium.Color.WHITE,
                        dashLength: 30, // 短划线长度
                    }),
                }
            })
            _this.markerList.push(obj)
        },
        // 加载所有航线航点
        drawAllMarker(){
            let _this = this
            _this.markerList.forEach(item => {
                _this.viewer.entities.removeById(item.topMarker)
                _this.viewer.entities.removeById(item.bottomMarker)
                _this.viewer.entities.removeById(item.line)
            })
            _this.markerList = []
            _this.fromData.routesPointList.forEach((item, index) => {
                item.index = index
                _this.drawMarker(item)
            })
            _this.drawLine()
        },
        // 加载航线
        drawLine(){
            let _this = this
            _this.lineIDList.forEach(item => {
                _this.viewer.entities.removeById(item)
            })
            _this.lineIDList = []
            let len = this.fromData.routesPointList.length
            if(len > 1) {
                for(let i = 1; i< len; i++) {
                    let loglat1 = this.fromData.routesPointList[i-1].coordinates.split(',')
                    let loglat2 = this.fromData.routesPointList[i].coordinates.split(',')
                    let position1 = Cesium.Cartesian3.fromDegrees(loglat1[0], loglat1[1], this.fromData.routesPointList[i-1].ellipsoidHeight)
                    let position2 = Cesium.Cartesian3.fromDegrees(loglat2[0], loglat2[1], this.fromData.routesPointList[i].ellipsoidHeight)
                    let lineID =  createUniqueString();
                    _this.lineIDList.push(lineID)
                    _this.viewer.entities.add({
                        id: lineID,
                        name: 'line', 
                        polyline: {
                            positions: [position1,position2], // 由点构线
                            width: 4.0, // 线的宽度
                            material: Cesium.Color.WHITE,
                        }
                    })
                }
            }
            _this.line_points_distance()


            // let path = _this.fromData.routesPointList.map(item => {
            //     let loglat = item.coordinates.split(',')
            //     let position = Cesium.Cartesian3.fromDegrees(loglat[0], loglat[1], item.ellipsoidHeight)
            //     return position;
            // })
            // if(path.length != 0) {
            //     if(_this.lineID) {
            //         _this.viewer.entities.removeById(_this.lineID)
            //     } else {
            //         _this.lineID =  createUniqueString();
            //     }
            //     _this.viewer.entities.add({
            //         id: _this.lineID,
            //         name: 'line', 
            //         polyline: {
            //             positions: path, // 由点构线
            //             width: 4.0, // 线的宽度
            //             material: Cesium.Color.WHITE,
            //         }
            //     })
            //     _this.line_points_distance()
            // } else {
            //     if(_this.lineID) {
            //         _this.viewer.entities.removeById(_this.lineID)
            //     }
            // }
            
        },
        // 计算航线总长度
        line_points_distance() {
            let _this = this
            let distance_count = 0
            _this.fromData.routesPointList.forEach((item,index) => {
                if(index != 0) {
                    let lonlat1 = _this.fromData.routesPointList[index - 1].coordinates.split(',')
                    let lonlat2 = item.coordinates.split(',')
                    var start_position = Cesium.Cartesian3.fromDegrees(lonlat1[0],lonlat1[1], _this.fromData.routesPointList[index - 1].ellipsoidHeight);
                    var end_position = Cesium.Cartesian3.fromDegrees(lonlat2[0], lonlat2[1], item.ellipsoidHeight);
                    distance_count += Cesium.Cartesian3.distance(start_position, end_position)
                }
            })
            _this.routesLength = Math.round(distance_count/ 10) / 100
        },
        // 获取当前相机信息
        getcameraPosInfo(){
            let _this = this
            // 获取 相机姿态信息
            var head = _this.viewer.scene.camera.heading 
            var pitch = _this.viewer.scene.camera.pitch
            var roll  = _this.viewer.scene.camera.roll
            var info ={'head': head ,'pitch': pitch ,'roll': roll};
            // 获取位置 wgs84的地心坐标系，x,y坐标值以弧度来表示
            var position = _this.viewer.scene.camera.positionCartographic
            // 弧度转经纬度
            var longitude = Cesium.Math.toDegrees(position.longitude).toFixed(6)
            var latitude =  Cesium.Math.toDegrees(position.latitude).toFixed(6)
            var height = position.height
            return {lng:longitude,lat:latitude,h:height,mat:info}
        },
        // 控制搜索空是否显示
        ShowAddressSearch(){
            if(this.isShowAddressSearch) {
                this.isShowAddressSearch = false
                this.addressSearch = ""
                this.addressOptions = []
                if(this.addressSearchId) {
                    this.viewer.entities.removeById(this.addressSearchId)
                }
            } else {
                this.isShowAddressSearch = true
            }
        },
            // 地址选择
        addressSelect(val){
            let index = this.addressOptions.findIndex(item => item.id == val)
            if(index != -1) {
                if(this.addressSearchId) {
                    this.viewer.entities.removeById(this.addressSearchId)
                }
                let jwd = this.addressOptions[index].location.split(",")
                let wgs84 = gcj02towgs84(jwd[0], jwd[1])
                this.addressSearchId = createUniqueString()
                this.viewer.entities.add({
                    id: this.addressSearchId,
                    position: Cesium.Cartesian3.fromDegrees(wgs84[0], wgs84[1]),
                    billboard: {
                        image: require("@/assets/images/mapicon/getLocation1.png"),
                        show: true,
                        width: 40, // default: undefined
                        height: 40, // default: undefined
                        disableDepthTestDistance: Number.POSITIVE_INFINITY, // 被遮挡是否可见（也就是将这个Entity在场景中置顶）
                        heightReference: Cesium.HeightReference.CLAMP_TO_GROUND, // 贴地
                    },
                    label: {
                        text: this.addressOptions[index].name,
                        font: '16px sans-serif', // 字体大小
                        style: Cesium.LabelStyle.FILL_AND_OUTLINE, // 字体样式
                        pixelOffset: new Cesium.Cartesian2(0, -25), // 偏移量
                        fillColor: Cesium.Color.fromCssColorString('#1296db'), // 字体填充色
                        outlineWidth: 5, // 字体外圈线宽度（同样也有颜色可设置）
                        outlineColor: Cesium.Color.fromCssColorString('#ffffff'),
                        verticalOrigin: Cesium.VerticalOrigin.BOTTOM, // 垂直位置
                        disableDepthTestDistance: Number.POSITIVE_INFINITY,
                        heightReference: Cesium.HeightReference.CLAMP_TO_GROUND, // 贴地
                    },
                })
                this.viewer.camera.flyTo({
                    destination: Cesium.Cartesian3.fromDegrees(wgs84[0], wgs84[1], 1500.0)
                })
            }
        },
        // 地址搜索
        remoteMethod(val){
            let _this = this
            if(val !== "" ) {
                this.addressSearchLoading = true
                _this.addressOptions = []
                getAssistantInputtips({keywords:val}).then(res => {
                    console.log("地址数据:", res)
                    _this.addressSearchLoading = false
                    if(res.data.info == "OK") {
                        res.data.tips.forEach(item => {
                            if(item.location.length != 0) {
                                _this.addressOptions.push(item)
                            }
                            
                        })
                        
                    }
                })
            } else {
                this.addressOptions = []
            }
        },

    },
    // 组件销毁前清理机场标记
    beforeDestroy() {
        this.clearAirportMarkers();
    }
};
</script>
  
  <style lang="scss" scoped>
.app-container {
  background: #f5f5f5;
  position: relative;
}
.w-map {
  width: 100%;
  height: 100%;
  border-radius: 10px;
  overflow: hidden;
  border: 1px solid rgba(73, 68, 78, 0.2);
}

.w-right {
    position: absolute;
    width: 300px;
    height: calc(100% - 80px);
    top: 40px;
    right: 40px;
    background: url("../../../assets/images/plan_bg.png");
    background-size: 100% 100%;
    opacity: 0.9;
    border-radius: 10px;
    padding: 20px;
    color: #fff;
    .w-line-name {
        width: 100%;
        height: 30px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-size: 14px;
        font-weight: 700;
        i {
            cursor: pointer;
            font-size: 18px;
        }
    }
    .w-info {
        width: 100%;
        height: 100px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        div {
            width: 32%;
            height: 50px;
            background: #1f1d24e6;
            border-radius: 5px;
            overflow: hidden;
            p {
                margin: 0;
                height: 25px;
                line-height: 25px;
                text-align: center;
                font-size: 12px;
                font-weight: 600;
            }
        }
    }
    .w-switch {
        width: 80%;
        height: 30px;
        font-size: 13px;
        display: flex;
        margin: 0 auto;
        div {
            width: 50%;
            height: 30px;
            text-align: center;
            line-height: 30px;
            cursor: pointer;
        }
        div:nth-child(1) {
            border: 1px solid #fff;
            border-radius: 15px 0 0 15px;
            border-right: none;
        }
        div:nth-child(2) {
            border: 1px solid #fff;
            border-radius: 0 15px 15px 0;
            border-left: none;
        }
        .w-active {
            background: #fff;
            color: #000;
        }
    }
    .w-line-info {
        width: 100%;
        height: calc(100% - 200px);
        overflow-x: hidden;
        overflow-y: auto;
        padding: 20px 0;
        .w-carousel {
            display: flex;
            width: 80%;
            height: 50px;
            margin: 0 auto;
            justify-content: space-between;
            align-items: center;
            font-size: 14px;
            color: #fff;
            i {
                cursor: pointer;
                padding: 10px;
            }
        }
        .w-line-info-box {
            width: 100%;
            height: calc(100% - 50px);
            overflow-x: hidden;
            overflow-y: auto;
        }
        .w-cell {
            width: 100%;
            padding: 5px 0;
            .w-title {
                width: 100%;
                height: 30px;
                display: flex;
                align-items: center;
                justify-content: space-between;
                font-size: 13px;
                .w-title-left {
                    width: 50%;
                    height: 30px;
                    display: flex;
                    align-items: center;
                    span {
                        margin-left: 5px;
                    }
                    i {
                        font-size: 14px;
                        cursor: pointer;
                    }
                }
                .w-title-right {
                    width: 50%;
                    height: 30px;
                    display: flex;
                    align-items: center;
                    justify-content: flex-end;
                    span {
                        margin-right: 5px;
                    }
                    i {
                        font-size: 14px;
                        cursor: pointer;
                    }
                }
            }
            .w-content {
                width: 100%;
                height: 40px;
                display: flex;
                justify-content: space-between;
                align-items: center;
                .w-content-img {
                    width: 20px;
                    height: 20px;
                }
                .w-content-slider {
                    width: calc(100% - 70px);
                    height: 40px;
                }
            }
        }
        .w-action {
            width: 100%;
        }
    }
    .w-btn {
        display: flex;
        justify-content: center;
        align-items: center;
    }
}


.w-right-tool {
    position: absolute;
    width: 45px;
    right: 360px;
    top: 40px;
    display: flex;
    flex-direction: column;
    img {
        width: 45px;
        height: 45px;
    cursor: pointer;
    }
}
.w-right-search {
    position: absolute;
    width: 260px;
    height: 40px;
    right: 420px;
    top: 43px;
}

.w-delete {
    width: 70px;
    height: 35px;
    text-align: center;
    font-size: 16px;
    color: #fff;
    line-height: 35px;
    background: #232323e1;
    position: absolute;
    cursor: pointer;
    border-radius: 5px;
    &:hover {
        background: #414040ec;
    }
}

.w-x-y-z {
    width: 200px;
    height: 300px;
    position: absolute;
    top: 20px;
    left: 20px;
    background: #414040ec;
    color: #ffffff;
    padding: 10px;
    .w-cell {
            width: 100%;
            padding: 5px 0;
            .w-title {
                width: 100%;
                height: 30px;
                display: flex;
                align-items: center;
                justify-content: space-between;
                font-size: 13px;
                .w-title-left {
                    width: 50%;
                    height: 30px;
                    display: flex;
                    align-items: center;
                    span {
                        margin-left: 5px;
                    }
                    i {
                        font-size: 14px;
                        cursor: pointer;
                    }
                }
                .w-title-right {
                    width: 50%;
                    height: 30px;
                    display: flex;
                    align-items: center;
                    justify-content: flex-end;
                    span {
                        margin-right: 5px;
                    }
                    i {
                        font-size: 14px;
                        cursor: pointer;
                    }
                }
            }
            .w-content {
                width: 100%;
                height: 40px;
                display: flex;
                justify-content: space-between;
                align-items: center;
                .w-content-img {
                    width: 20px;
                    height: 20px;
                }
                .w-content-slider {
                    width: calc(100% - 70px);
                    height: 40px;
                }
                // color: #00bb7d36;
            }
        }
}

::v-deep {
    .amap-marker-label {
        background: none;
        color: #fff;
        border: none;
        font-weight: 700;
        font-size: 13px;
        cursor: pointer;
    }
    // .w-line-info {
        ::-webkit-scrollbar {
            display: none;
        }
    // }
}

/* 禁用状态样式 */
.disabled {
    opacity: 0.5 !important;
    cursor: not-allowed !important;
    pointer-events: none !important;
}

.w-carousel i.disabled {
    color: #ccc !important;
    cursor: not-allowed !important;
}

.w-title-right i.disabled {
    color: #ccc !important;
    cursor: not-allowed !important;
}

.el-icon-rank.disabled {
    color: #ccc !important;
    cursor: not-allowed !important;
}
</style>