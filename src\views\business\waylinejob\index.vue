<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="任务ID" prop="jobId">
        <el-input v-model="queryParams.jobId" placeholder="请输入任务ID" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="任务名称" prop="name">
        <el-input v-model="queryParams.name" placeholder="请输入任务名称" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="机场SN" prop="dockSn">
        <el-input v-model="queryParams.dockSn" placeholder="请输入机场SN" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        <el-button type="success" icon="el-icon-plus" size="mini" @click="handleAddWaylineJob">新增航线任务</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <!-- <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['business:waylinejob:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['business:waylinejob:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['business:waylinejob:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['business:waylinejob:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar> -->
    </el-row>

    <el-table v-loading="loading" :data="waylinejobList">
      <!-- <el-table-column type="selection" width="55" align="center" /> -->
      <!-- <el-table-column label="序号" align="center" prop="id" /> -->
      <el-table-column label="任务ID" align="center" prop="jobId" />
      <el-table-column label="任务名称" align="center" prop="name" />
      <!-- <el-table-column label="关联文件" align="center" prop="fileId" /> -->
      <el-table-column label="机场SN" align="center" prop="dockSn" />
      <!-- <el-table-column label="工作空间" align="center" prop="workspaceId" /> -->
      <el-table-column label="任务类型" align="center" prop="taskType">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.wayline_job_type" :value="scope.row.taskType" />
        </template>
      </el-table-column>
      <!-- <el-table-column label="航线类型" align="center" prop="waylineType" /> -->
      <!-- <el-table-column label="任务执行时间" align="center" prop="executeTime" />
      <el-table-column label="任务结束时间" align="center" prop="completedTime" /> -->
      <el-table-column label="任务执行时间" align="center" prop="executeTime">
        <template #default="scope">
          {{ parseTime(scope.row.executeTime) }}
        </template>
      </el-table-column>
      <el-table-column label="任务结束时间" align="center" prop="completedTime">
        <template #default="scope">
          {{ parseTime(scope.row.completedTime) }}
        </template>
      </el-table-column>
      <el-table-column label="创建人" align="center" prop="username" />
      <!-- <el-table-column label="计划开始时间" align="center" prop="beginTime" /> -->
      <!-- <el-table-column label="计划结束时间" align="center" prop="endTime" /> -->
      <el-table-column label="计划开始时间" align="center" prop="beginTime">
        <template #default="scope">
          {{ parseTime(scope.row.beginTime) }}
        </template>
      </el-table-column>
      <el-table-column label="计划结束时间" align="center" prop="endTime">
        <template #default="scope">
          {{ parseTime(scope.row.endTime) }}
        </template>
      </el-table-column>
      <!-- <el-table-column label="任务状态" align="center" prop="status" /> -->
      <!-- <el-table-column label="任务状态" align="center" prop="status">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.wayline_job_status" :value="scope.row.status"/>
        </template>
      </el-table-column>
      <el-table-column label="错误码" align="center" prop="errorCode" /> -->

      <el-table-column label="任务状态" align="center" prop="status">
        <template slot-scope="scope">
          <el-tooltip :content="scope.row.errorMsg" placement="top" :disabled="scope.row.status === 3">
            <div style="display: inline-flex; align-items: center">
              <dict-tag :options="dict.type.wayline_job_status" :value="scope.row.status" />
              <i v-if="scope.row.status !== 3" class="el-icon-warning-outline"
                style="margin-left: 4px; color: #f56c6c;"></i>
            </div>
          </el-tooltip>
        </template>
      </el-table-column>
      <!-- <el-table-column label="错误码" align="center" prop="errorCode" /> -->
      <el-table-column label="返航高度(m)" align="center" prop="rthAltitude" />
      <!-- <el-table-column label="失控执行" align="center" prop="outOfControl" /> -->
      <el-table-column label="失控执行" align="center" prop="outOfControl">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.wayline_job_loss_control" :value="scope.row.outOfControl" />
        </template>
      </el-table-column>
      <!-- <el-table-column label="媒体文件数量" align="center" prop="mediaCount" /> -->

      <el-table-column label="媒体文件数量" align="center">
        <template #default="{ row }">
          <div class="upload-status">
            <span v-if="row.mediaCount === 0" class="status-dot yellow"></span>
            <span v-else-if="row.uploadedCount"
              :class="['status-dot', row.mediaCount === row.uploadedCount ? 'green' : 'blue']"></span>
            <span class="status-text">
              {{ row.mediaCount === 0 ?
                '没有媒体文件' :
                row.mediaCount === row.uploadedCount ?
                  `已上传(${row.uploadedCount}/${row.mediaCount})` :
                  `待上传(${row.uploadedCount}/${row.mediaCount})` }}
            </span>
          </div>
        </template>
      </el-table-column>
      <!-- <el-table-column label="" align="center" prop="createTime" />
      <el-table-column label="" align="center" prop="updateTime" />
      <el-table-column label="" align="center" prop="parentId" /> -->
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button v-if="scope.row.status === 1 || scope.row.status === 2" size="mini" type="text"
            icon="el-icon-close" @click="handleCancelTask(scope.row)" style="color: #f56c6c;">取消任务</el-button>

          <el-button v-if="scope.row.status === 1 || scope.row.status === 2"  size="mini" type="text" icon="el-icon-video-camera" @click="handleRealTimeScene(scope.row)"
            style="color: #409EFF;">实时场景</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />

    <!-- 实时场景弹窗 -->
    <el-dialog :title="title" :visible.sync="open" width="700px" append-to-body @close="handleDialogClose">
      <!-- 视频流播放器 -->
      <div class="video-container">
        <div id="Player"></div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancel">关 闭</el-button>
      </div>
    </el-dialog>

    <!-- 新增航线任务弹窗 -->
    <el-dialog title="新增航线任务" :visible.sync="addWaylineJobOpen" width="500px" append-to-body>
      <el-form :model="waylineJobForm" :rules="waylineJobRules" ref="waylineJobForm" label-width="100px">
        <el-form-item label="机场" prop="airportSn">
          <el-select v-model="waylineJobForm.airportSn" placeholder="请选择机场" style="width: 100%">
            <el-option
              v-for="airport in airportList"
              :key="airport.airportSn"
              :label="airport.airportName"
              :value="airport.airportSn">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="航线" prop="routesId">
          <el-select v-model="waylineJobForm.routesId" placeholder="请选择航线" style="width: 100%">
            <el-option
              v-for="route in routesList"
              :key="route.routesId"
              :label="route.routesName"
              :value="route.routesId">
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancelAddWaylineJob">取 消</el-button>
        <el-button type="primary" @click="confirmAddWaylineJob" :loading="addWaylineJobLoading">确 认</el-button>
      </div>
    </el-dialog>

  </div>
</template>

<style scoped>
.upload-status {
  display: flex;
  align-items: center;
  gap: 6px;
}

.status-dot {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.green {
  background-color: #67C23A;
}

.blue {
  background-color: #409EFF;
}

.yellow {
  background-color: #E6A23C;
}

.status-text {
  font-size: 14px;
}
.video-container {
  width: 100%;
  height: 400px;
  background-color: #000;
  display: flex;
  align-items: center;
  justify-content: center;
}
#Player {
  width: 100%;
  height: 100%;
}
</style>

<script>
import { listWaylinejob, getWaylinejob, delWaylinejob, addWaylinejob, updateWaylinejob, cancelTask } from "@/api/business/waylinejob";
import { listAirport } from "@/api/cruise/airdrome";
import { routesList } from "@/api/cruise/airline";
import request from '@/utils/request';

// 创建手动飞行任务API
function createHandFlyJob(data) {
  return request({
    url: '/business/device/control/createHandFlyJob',
    method: 'post',
    data: data
  });
}

// 启动视频流API
function startLiveStream(data) {
  return request({
    url: '/business/live/streams/start',
    method: 'post',
    data: data
  });
}

// 停止视频流API
function stopLiveStream(data) {
  return request({
    url: '/business/live/streams/stop',
    method: 'post',
    data: data
  });
}

export default {
  name: "Waylinejob",
  dicts: ['wayline_job_type', 'wayline_job_status', 'wayline_job_loss_control'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // Wayline mission information of the dock.表格数据
      waylinejobList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        jobId: null,
        name: null,
        fileId: null,
        dockSn: null,
        workspaceId: null,
        taskType: null,
        waylineType: null,
        executeTime: null,
        completedTime: null,
        username: null,
        beginTime: null,
        endTime: null,
        errorCode: null,
        status: null,
        rthAltitude: null,
        outOfControl: null,
        mediaCount: null,
        createTime: null,
        updateTime: null,
        parentId: null
      },
      // 表单参数
      form: {},
      // 视频播放器
      player: null,
      // 视频流地址
      videoUrl: '',
      // 视频流参数
      streamParams: null,
      // 表单校验
      rules: {
        jobId: [
          { required: true, message: "任务ID不能为空", trigger: "blur" }
        ],
        name: [
          { required: true, message: "任务名称不能为空", trigger: "blur" }
        ],
        fileId: [
          { required: true, message: "关联文件不能为空", trigger: "blur" }
        ],
        dockSn: [
          { required: true, message: "机场SN不能为空", trigger: "blur" }
        ],
        workspaceId: [
          { required: true, message: "工作空间不能为空", trigger: "blur" }
        ],
        taskType: [
          { required: true, message: "任务类型不能为空", trigger: "change" }
        ],
        waylineType: [
          { required: true, message: "航线类型不能为空", trigger: "change" }
        ],
        username: [
          { required: true, message: "创建人不能为空", trigger: "blur" }
        ],
        beginTime: [
          { required: true, message: "计划开始时间不能为空", trigger: "blur" }
        ],
        status: [
          { required: true, message: "任务状态1: pending; 2: in progress; 3: success; 4: cancel; 5: failed不能为空", trigger: "change" }
        ],
        rthAltitude: [
          { required: true, message: "返航高度 min: 20m; max: 500m不能为空", trigger: "blur" }
        ],
        outOfControl: [
          { required: true, message: "失控执行 0: go home; 1: hover; 2: landing;不能为空", trigger: "blur" }
        ],
        mediaCount: [
          { required: true, message: "媒体文件数量不能为空", trigger: "blur" }
        ],
        createTime: [
          { required: true, message: "不能为空", trigger: "blur" }
        ],
        updateTime: [
          { required: true, message: "不能为空", trigger: "blur" }
        ],
      },
      // 当前任务
      currentTask: null,
      // 新增航线任务弹窗
      addWaylineJobOpen: false,
      addWaylineJobLoading: false,
      // 机场列表
      airportList: [],
      // 航线列表
      routesList: [],
      // 新增航线任务表单
      waylineJobForm: {
        airportSn: '',
        routesId: ''
      },
      // 新增航线任务表单验证规则
      waylineJobRules: {
        airportSn: [
          { required: true, message: "请选择机场", trigger: "change" }
        ],
        routesId: [
          { required: true, message: "请选择航线", trigger: "change" }
        ]
      }
    };
  },
  created() {
    this.getList();
    this.getAirportList();
    this.getRoutesList();
  },
  methods: {
    /** 查询Wayline mission information of the dock.列表 */
    getList() {
      this.loading = true;
      listWaylinejob(this.queryParams).then(response => {
        this.waylinejobList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
      // 停止视频流
      this.stopVideoStream();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        jobId: null,
        name: null,
        fileId: null,
        dockSn: null,
        workspaceId: null,
        taskType: null,
        waylineType: null,
        executeTime: null,
        completedTime: null,
        username: null,
        beginTime: null,
        endTime: null,
        errorCode: null,
        status: null,
        rthAltitude: null,
        outOfControl: null,
        mediaCount: null,
        createTime: null,
        updateTime: null,
        parentId: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加Wayline mission information of the dock.";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getWaylinejob(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改Wayline mission information of the dock.";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateWaylinejob(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addWaylinejob(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除Wayline mission information of the dock.编号为"' + ids + '"的数据项？').then(function () {
        return delWaylinejob(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => { });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('business/waylinejob/export', {
        ...this.queryParams
      }, `waylinejob_${new Date().getTime()}.xlsx`)
    },
    /** 取消任务操作 */
    handleCancelTask(row) {
      this.$modal.confirm(`是否确认取消任务"${row.name}"？`).then(() => {
        return cancelTask(row.jobId);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("任务取消成功");
      }).catch(() => { });
    },
    /** 处理实时场景按钮 */
    handleRealTimeScene(row) {
      this.currentTask = row;
      this.title = `实时场景`;

      // 构建视频流启动参数
      this.streamParams = {
        "url": "rmtp://1.92.110.9/live",
        "url_type": 0,
        "video_id": `${row.dockSn}/99-0-0/normal-0`,
        "video_quality": 0
      };

      // 先调用启动视频流接口
      this.startVideoStream();
    },
    /** 启动视频流 */
    startVideoStream() {
      startLiveStream(this.streamParams).then(response => {
        if (response.code === 200 && response.data && response.data.url) {
          this.videoUrl = response.data.url;
          this.open = true;
          // 初始化视频播放器
          this.initVideoPlayer();
        } else {
          this.$modal.msgError("获取视频流地址失败");
        }
      }).catch(error => {
        console.error('启动视频流失败:', error);
        this.$modal.msgError("启动视频流失败");
      });
    },

    /** 初始化视频播放器 */
    initVideoPlayer() {
      this.$nextTick(() => {
        if (window.WasmPlayer && document.getElementById('Player')) {
          // 实例化播放器
          this.player = new WasmPlayer(null, 'Player', this.videoCallbackFun);
          // 调用播放
          this.player.play(this.videoUrl, 1);
        } else {
          console.error('WasmPlayer未加载或Player元素不存在');
        }
      });
    },
    /** 视频播放事件回调 */
    videoCallbackFun(e) {
      console.log('视频播放回调:', e);
    },

    /** 弹窗关闭处理 */
    handleDialogClose() {
      // 销毁视频播放器
      if (this.player) {
        try {
          this.player.stop();
          this.player = null;
        } catch (error) {
          console.error('销毁视频播放器失败:', error);
        }
      }

      // 停止视频流
      this.stopVideoStream();
    },

    /** 停止视频流 */
    stopVideoStream() {
      if (this.streamParams) {
        stopLiveStream(this.streamParams).then(response => {
          console.log('视频流已停止');
        }).catch(error => {
          console.error('停止视频流失败:', error);
        });
      }
    },

    /** 获取机场列表 */
    getAirportList() {
      listAirport({ pageNum: 1, pageSize: 999 }).then(response => {
        this.airportList = response.rows || [];
      });
    },

    /** 获取航线列表 */
    getRoutesList() {
      routesList({ pageNum: 1, pageSize: 999 }).then(response => {
        this.routesList = response.rows || [];
      });
    },

    /** 新增航线任务按钮操作 */
    handleAddWaylineJob() {
      this.waylineJobForm = {
        airportSn: '',
        routesId: ''
      };
      this.addWaylineJobOpen = true;
    },

    /** 取消新增航线任务 */
    cancelAddWaylineJob() {
      this.addWaylineJobOpen = false;
      this.waylineJobForm = {
        airportSn: '',
        routesId: ''
      };
      this.$refs.waylineJobForm.resetFields();
    },

    /** 确认新增航线任务 */
    confirmAddWaylineJob() {
      this.$refs.waylineJobForm.validate(valid => {
        if (valid) {
          this.addWaylineJobLoading = true;
          const data = {
            airportSn: this.waylineJobForm.airportSn,
            routesId: this.waylineJobForm.routesId
          };

          createHandFlyJob(data).then(response => {
            this.$modal.msgSuccess("新增航线任务成功");
            this.addWaylineJobOpen = false;
            this.addWaylineJobLoading = false;
            this.getList(); // 刷新列表
            this.cancelAddWaylineJob();
          }).catch(() => {
            this.addWaylineJobLoading = false;
          });
        }
      });
    },
  },
  beforeDestroy() {
    // 销毁视频播放器
    if (this.player) {
      try {
        this.player.stop();
        this.player = null;
      } catch (error) {
        console.error('销毁视频播放器失败:', error);
      }
    }
  }
};
</script>
