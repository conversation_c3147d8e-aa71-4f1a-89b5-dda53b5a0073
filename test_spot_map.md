# 景点管理地图选择功能测试

## 功能说明
在景点管理的添加/编辑表单中，在"选择机场"下方添加了"请选择景区经纬度"功能区域。

## 实现的功能
1. **地图显示**: 在表单中显示一个300px高度的地图容器
2. **地图点击选择**: 用户可以在地图上点击选择位置
3. **经纬度回显**: 选择位置后，在地图下方显示经纬度输入框（只读）
4. **标记显示**: 在选择的位置显示地图标记
5. **数据保存**: 经纬度数据会保存到表单的latitude和longitude字段

## 测试步骤
1. 打开景点管理页面
2. 点击"新增"按钮打开添加表单
3. 在"选择机场"下方应该看到"请选择景区经纬度"区域
4. 地图应该正常加载显示
5. 在地图上点击任意位置
6. 应该在点击位置显示标记
7. 地图下方应该显示经纬度输入框，显示选择的坐标
8. 经纬度输入框应该是只读状态

## 技术实现
- 使用高德地图API (AMapLoader)
- 地图容器ID: "spot-map"
- 地图样式: "amap://styles/whitesmoke"
- 标记图标: "@/assets/images/mapicon/a16.png"
- 经纬度精度: 6位小数

## 数据字段
- form.latitude: 纬度
- form.longitude: 经度
- 表单验证: coordinates字段验证经纬度是否已选择

## 注意事项
- 地图在弹窗打开时初始化
- 如果编辑已有数据且包含经纬度，会在地图上显示已有位置
- 表单重置时会清除地图标记
